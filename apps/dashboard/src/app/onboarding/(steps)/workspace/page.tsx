import * as React from "react";
import { Metada<PERSON> } from "next";
import WorkspaceOnboardingForm from "@/src/features/auth/components/workspace-onboarding-form";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Create Workspace | Centaly"
};

export default async function WorkspaceOnboardingPage() {
  return (
    <AuthContainer maxWidth="md">
      <WorkspaceOnboardingForm />
    </AuthContainer>
  );
}
